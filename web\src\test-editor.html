<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editor 智能提示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .test-steps {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .test-steps li {
            margin-bottom: 5px;
        }
        .editor-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            height: 200px;
        }
        .variable-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Editor 智能提示测试页面</h1>
        
        <div class="test-section">
            <div class="test-title">测试说明</div>
            <div class="test-description">
                这个页面用于测试 Monaco Editor 的智能提示功能是否正常工作。
                智能提示应该能够：
                <ul>
                    <li>显示当前变量（临时变量）</li>
                    <li>显示 _data. 后的局部和全局变量</li>
                    <li>显示嵌套对象的子属性</li>
                    <li>显示 Utils. 后的函数列表</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">测试 1: 基础智能提示</div>
            <div class="variable-info">
                <strong>模拟变量数据：</strong><br>
                当前变量: userName (string), userAge (number)<br>
                局部变量: _data.orderInfo (object), _data.productList (array)<br>
                全局变量: _data.systemConfig (object)
            </div>
            <div class="test-steps">
                <strong>测试步骤：</strong>
                <ol>
                    <li>在编辑器中输入字母，应该看到 userName, userAge 等当前变量的提示</li>
                    <li>输入 "_data." 应该看到 orderInfo, productList, systemConfig 的提示</li>
                    <li>输入 "Utils." 应该看到函数列表的提示</li>
                    <li>按 Ctrl+Space 可以手动触发智能提示</li>
                </ol>
            </div>
            <div class="editor-container" id="editor1"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试 2: 嵌套对象智能提示</div>
            <div class="variable-info">
                <strong>模拟嵌套数据：</strong><br>
                orderInfo.orderId (string), orderInfo.customer.name (string), orderInfo.customer.phone (string)<br>
                systemConfig.database.host (string), systemConfig.database.port (number)
            </div>
            <div class="test-steps">
                <strong>测试步骤：</strong>
                <ol>
                    <li>输入 "_data.orderInfo." 应该看到 orderId, customer 的提示</li>
                    <li>输入 "_data.orderInfo.customer." 应该看到 name, phone 的提示</li>
                    <li>输入 "_data.systemConfig.database." 应该看到 host, port 的提示</li>
                </ol>
            </div>
            <div class="editor-container" id="editor2"></div>
        </div>

        <div class="test-section">
            <div class="test-title">问题诊断</div>
            <div class="test-description">
                如果智能提示没有正常工作，可能的原因：
                <ul>
                    <li>Monaco Editor 没有正确加载智能提示配置</li>
                    <li>变量数据没有正确传递给智能提示系统</li>
                    <li>智能提示的正则表达式匹配有问题</li>
                    <li>嵌套对象的查找逻辑有错误</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 这里应该集成 Vue 组件和 Monaco Editor
        // 由于这是一个静态 HTML 文件，实际测试需要在 Vue 应用中进行
        console.log('Editor 智能提示测试页面已加载');
        console.log('请在 Vue 应用中使用 ValueDialog 组件进行实际测试');
    </script>
</body>
</html>
