using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.VisualFunction;
using System.Diagnostics;

namespace GCP.Functions.Common.Services
{
    /// <summary>
    /// 可视化函数服务
    /// </summary>
    [Function("visualFunction", "可视化函数服务")]
    internal class VisualFunctionService : BaseService
    {
        /// <summary>
        /// 执行可视化函数
        /// </summary>
        /// <param name="request">执行请求</param>
        /// <returns>执行结果</returns>
        [Function("execute", "执行可视化函数")]
        public async Task<VisualFunctionExecuteResponse> ExecuteAsync(VisualFunctionExecuteRequest request)
        {
            try
            {


                var stopwatch = Stopwatch.StartNew();
                var engine = new VisualFunctionEngine(this.Context.db);
                var response = new VisualFunctionExecuteResponse();

                // 生成C#表达式
                response.GeneratedExpression = engine.GenerateCSharpExpression(request.Steps);

                // 如果只需要生成表达式，直接返回
                if (request.GenerateExpressionOnly)
                {
                    response.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;
                    return response;
                }

                // 执行函数
                if (request.UseCompiledExpression)
                {
                    // 使用编译表达式执行（高性能模式）
                    response.Result = await engine.ExecuteAsync(request.Steps, request.InputData, true);

                    // 生成简化的步骤结果详情
                    response.StepResults = request.Steps.OrderBy(s => s.Order).Select(step => new VisualFunctionStepResult
                    {
                        StepId = step.Id,
                        StepName = step.DisplayName,
                        Result = $"步骤 {step.Order + 1} 执行完成",
                        ExecutionTimeMs = response.ExecutionTimeMs / request.Steps.Count, // 平均分配时间
                        Success = true
                    }).ToList();
                }
                else
                {
                    // 使用解释执行（调试模式，获取详细步骤信息）
                    var context = new VisualFunctionContext
                    {
                        InputData = request.InputData ?? new Dictionary<string, object>(),
                        Variables = new Dictionary<string, object>(),
                        StepResults = new Dictionary<string, object>()
                    };

                    response.Result = await engine.ExecuteInterpretedWithDetailsAsync(request.Steps, context);
                    response.StepResults = context.StepExecutionDetails;
                }

                response.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;

                return response;
            }
            catch (Exception ex)
            {
                return new VisualFunctionExecuteResponse
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    ExecutionTimeMs = 0,
                    StepResults = request.Steps.Select(step => new VisualFunctionStepResult
                    {
                        StepId = step.Id,
                        StepName = step.DisplayName,
                        Success = false,
                        ErrorMessage = ex.Message
                    }).ToList()
                };
            }
        }

        /// <summary>
        /// 测试单个函数步骤
        /// </summary>
        /// <param name="step">函数步骤</param>
        /// <param name="inputData">输入数据</param>
        /// <returns>测试结果</returns>
        [Function("testStep", "测试单个函数步骤")]
        public async Task<object> TestStepAsync(VisualFunctionStep step, Dictionary<string, object>? inputData = null)
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();
                var engine = new VisualFunctionEngine(this.Context.db);
                
                var steps = new List<VisualFunctionStep> { step };
                var result = await engine.ExecuteAsync(steps, inputData ?? new Dictionary<string, object>());
                
                return new
                {
                    Success = true,
                    Result = result,
                    ExecutionTimeMs = stopwatch.ElapsedMilliseconds
                };
            }
            catch (Exception ex)
            {
                return new
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 获取可用的内置函数列表
        /// </summary>
        /// <returns>函数列表</returns>
        [Function("getBuiltinFunctions", "获取内置函数列表")]
        public List<object> GetBuiltinFunctions()
        {
            try
            {
                var functions = GetBuiltinFunctionList();
                return functions;
            }
            catch (Exception ex)
            {
                throw new CustomException($"获取内置函数列表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 验证可视化函数配置
        /// </summary>
        /// <param name="steps">函数步骤列表</param>
        /// <returns>验证结果</returns>
        [Function("validate", "验证可视化函数配置")]
        public object ValidateConfiguration(List<VisualFunctionStep> steps)
        {
            try
            {
                var validationResult = ValidateSteps(steps);
                return validationResult;
            }
            catch (Exception ex)
            {
                throw new CustomException($"验证配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 保存可视化函数配置（已移除，不再需要单独的配置表）
        /// </summary>
        /// <param name="configuration">函数配置</param>
        /// <returns>保存结果</returns>
        [Function("saveConfiguration", "保存可视化函数配置")]
        public async Task<object> SaveConfigurationAsync(VisualFunctionConfiguration configuration)
        {
            // 可视化函数配置不再需要单独保存，直接在使用时执行
            return new { Success = true, Message = "可视化函数配置无需保存，直接执行即可" };
        }

        /// <summary>
        /// 获取保存的可视化函数配置列表（已移除）
        /// </summary>
        /// <returns>配置列表</returns>
        [Function("getConfigurations", "获取可视化函数配置列表")]
        public async Task<List<VisualFunctionConfiguration>> GetConfigurationsAsync()
        {
            // 不再需要保存配置，返回空列表
            return new List<VisualFunctionConfiguration>();
        }

        /// <summary>
        /// 删除可视化函数配置（已移除）
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>删除结果</returns>
        [Function("deleteConfiguration", "删除可视化函数配置")]
        public async Task<object> DeleteConfigurationAsync(string id)
        {
            // 不再需要删除配置
            return new { Success = true, Message = "无需删除配置" };
        }

        #region 私有方法

        /// <summary>
        /// 获取内置函数列表
        /// </summary>
        private List<object> GetBuiltinFunctionList()
        {
            var functions = new List<object>();
            var utilsType = typeof(ScriptExtensions.JavascriptUtils);
            var methods = utilsType.GetMethods(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance)
                .Where(m => !m.IsSpecialName && m.DeclaringType == utilsType);

            foreach (var method in methods)
            {
                var parameters = method.GetParameters().Select(p => new
                {
                    Name = p.Name,
                    Type = p.ParameterType.Name,
                    Required = !p.HasDefaultValue,
                    DefaultValue = p.HasDefaultValue ? p.DefaultValue : null
                }).ToList();

                functions.Add(new
                {
                    Name = method.Name,
                    DisplayName = GetFunctionDisplayName(method.Name),
                    Description = GetFunctionDescription(method.Name),
                    Parameters = parameters,
                    ReturnType = method.ReturnType.Name
                });
            }

            return functions;
        }

        /// <summary>
        /// 获取函数显示名称
        /// </summary>
        private string GetFunctionDisplayName(string functionName)
        {
            return functionName switch
            {
                "STRING_CONCAT" => "字符串拼接",
                "STRING_SUBSTRING" => "字符串截取",
                "STRING_REPLACE" => "字符串替换",
                "ARRAY_LENGTH" => "数组长度",
                "DATE_ADD" => "日期加减",
                "DICT_GET" => "获取字典值",
                "MATH_CALC" => "数学计算",
                _ => functionName
            };
        }

        /// <summary>
        /// 获取函数描述
        /// </summary>
        private string GetFunctionDescription(string functionName)
        {
            return functionName switch
            {
                "STRING_CONCAT" => "将多个值拼接成字符串",
                "STRING_SUBSTRING" => "截取字符串的指定部分",
                "STRING_REPLACE" => "替换字符串中的指定内容",
                "ARRAY_LENGTH" => "获取数组或集合的长度",
                "DATE_ADD" => "对日期进行加减运算",
                "DICT_GET" => "从字典中获取指定键的值",
                "MATH_CALC" => "执行基本的数学运算",
                _ => $"执行 {functionName} 函数"
            };
        }

        /// <summary>
        /// 验证函数步骤
        /// </summary>
        private object ValidateSteps(List<VisualFunctionStep> steps)
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            if (steps == null || steps.Count == 0)
            {
                errors.Add("至少需要一个函数步骤");
                return new { Success = false, Errors = errors, Warnings = warnings };
            }

            foreach (var step in steps)
            {
                // 验证函数名称
                if (string.IsNullOrEmpty(step.FunctionName))
                {
                    errors.Add($"步骤 {step.Order + 1} 缺少函数名称");
                }

                // 验证参数
                foreach (var param in step.Parameters)
                {
                    if (param.Required && (param.Value == null || string.IsNullOrEmpty(param.Value.ToString())))
                    {
                        errors.Add($"步骤 {step.Order + 1} 的必需参数 '{param.Name}' 不能为空");
                    }
                }

                // 检查输出变量名冲突
                if (!string.IsNullOrEmpty(step.OutputVariable))
                {
                    var duplicates = steps.Where(s => s.Id != step.Id && s.OutputVariable == step.OutputVariable).ToList();
                    if (duplicates.Any())
                    {
                        warnings.Add($"输出变量名 '{step.OutputVariable}' 在多个步骤中重复使用");
                    }
                }
            }

            return new
            {
                Success = errors.Count == 0,
                Errors = errors,
                Warnings = warnings
            };
        }

        #endregion
    }
}
